<template>
  <!--入口页面-->
  <div class="app-container">
    <div class="app-title">
      <div class="app-title-tab">
        <span :class="{'app-title-tab-selected': selectedTab === 'Scar'}" @click="switchTab('Scar')">Scar</span>
        <span :class="{'app-title-tab-selected': selectedTab === 'Overview'}" @click="switchTab('Overview')">Overview</span>
      </div>
      <div style="flex: 0 1 940px">

      </div>
    </div>

    <!-- 使用v-if重新渲染组件 -->
    <scar-list
      v-if="selectedTab === 'Scar'"
      :key="'scar-' + tabKey"
      :item-arr="itemArr"
    />

    <scar-overview
      v-if="selectedTab === 'Overview'"
      :key="'overview-' + tabKey"
      @switch-tab="switchTab"
    />
  </div>
</template>

<script>
import { statisticsCard } from '@/api/scar'
import event from '@/views/dashboard/mixins/event'
import ScarList from './index/scarList.vue'
import ScarOverview from './index/overview.vue'

export default {
  name: 'ScarIndex',
  components: {
    ScarList,
    ScarOverview
  },
  mixins: [event],

  data() {
    return {
      // 当前选中的标签页
      selectedTab: 'Scar',
      // 用于强制重新渲染的key
      tabKey: 0,
      // 统计卡片数据
      itemArr: []
    }
  },
  mounted() {
    this.getStatisticsCard()
  },
  methods: {
    // 切换标签页
    switchTab(tab) {
      if (this.selectedTab !== tab) {
        this.selectedTab = tab
        // 增加key值强制重新渲染
        this.tabKey++
      }
    },

    // 获取统计卡片数据
    getStatisticsCard() {
      statisticsCard().then(res => {
        this.itemArr = [
          { label: 'YTD 发布SCAR', value: res.data.publishCount },
          { label: 'YTD 发布进料检验改善单', value: res.data.publishIqcCount },
          { label: 'YTD 发布在线不良改善单', value: res.data.publishScCount },
          { label: 'YTD 已完成SCAR个数', value: res.data.completedCount },
          { label: 'YTD 进行中SCAR', value: res.data.underWayCount }
        ]
      }).catch(_ => {
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.greenDot {
  height: 20px;
  width: 20px;
  background-color: #008000;
  border-radius: 50%;
  display: inline-block;
}
.redDot {
  height: 20px;
  width: 20px;
  background-color: #ff0000;
  border-radius: 50%;
  display: inline-block;
}
.searchItem{
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;
  ::v-deep .el-form-item__content{
    width: calc(100% - 170px);
  }
}
.searchValue{
  width: 95%;
}

.app-title{
  display: flex;
  margin-bottom: 0px;
  justify-content: space-between;
  &-tab{
    flex: none;
    font-size: 24px;
    color: #383838;
    letter-spacing: 0;
    font-weight: 400;
    cursor: pointer;
    span{
      margin: 0 15px;
    }
    &-selected{
      font-size: 28px;
      color: #383838;
      letter-spacing: 0;
      font-weight: 700;
      position: relative;
    }
    &-selected::after {
      content: "";
      position: absolute;
      border-radius: 2px;
      width: 20px; /* Adjust the width of the line */
      height: 4px; /* Adjust the thickness of the line */
      background-color: black; /* Adjust the line color */
      bottom: -6px; /* Adjust the position of the line relative to the text */
      left: 50%;
      transform: translateX(-50%);
    }
  }
}

</style>
