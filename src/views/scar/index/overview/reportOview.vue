
<template>
<div>
  <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
    <el-input
      v-model="queryParams.supplierIds"
      style="flex: 0 1 40%"
      :placeholder="$t('供应商')"
      clearable
      @keyup.enter.native="doSearch"
    />
    <el-select
      style="width: 130px"
      v-model="queryParams.year"
      :placeholder="$t('年份')"
    >
      <el-option label="2025" value="2025" />
      <el-option label="2024" value="2024" />
      <el-option label="2023" value="2023" />
    </el-select>
    <el-button plain type="primary" @click="queryParams.pageNo = 1;doSearch();">{{ $t('common.search') }}</el-button>
    <el-button style="margin-left: 0" @click="resetQuery">{{ $t('common.reset') }}</el-button>
  </div>
  <div style="margin: 5px 0">
    <el-button type="primary" icon="el-icon-download" @click="downloadReport">下载报告</el-button>

  </div>
  <div v-if="showMode === 'list'">
    <!-- 汇总报告表格 -->
    <el-table
      :data="list"
      border
      stripe
      v-loading="loading"
    >
      <!-- 供应商信息 -->
      <el-table-column prop="nameShort" label="供应商简称" width="200" fixed="left" />

      <!-- 月份数据 -->
      <el-table-column prop="jan" label="1月" width="60" align="center" />
      <el-table-column prop="feb" label="2月" width="60" align="center" />
      <el-table-column prop="mar" label="3月" width="60" align="center" />
      <el-table-column prop="apr" label="4月" width="60" align="center" />
      <el-table-column prop="may" label="5月" width="60" align="center" />
      <el-table-column prop="jun" label="6月" width="60" align="center" />
      <el-table-column prop="jul" label="7月" width="60" align="center" />
      <el-table-column prop="aug" label="8月" width="60" align="center" />
      <el-table-column prop="sept" label="9月" width="60" align="center" />
      <el-table-column prop="oct" label="10月" width="60" align="center" />
      <el-table-column prop="nov" label="11月" width="60" align="center" />
      <el-table-column prop="dece" label="12月" width="60" align="center" />

      <!-- 总体统计 -->
      <el-table-column prop="open" label="开放" width="80" align="center" />
      <el-table-column prop="delay" label="延期" width="80" align="center" />
      <el-table-column prop="total" label="总数" width="80" align="center" />
      <el-table-column prop="closed" label="已关闭" width="80" align="center" />
      <el-table-column prop="completionRate" label="完成率" width="100" align="center">
        <template slot-scope="scope">
          {{ (scope.row.completionRate * 100).toFixed(2) }}%
        </template>
      </el-table-column>

      <!-- WF统计 -->
      <el-table-column label="IQC统计" align="center">
        <el-table-column prop="wfOpen" label="开放" width="80" align="center" />
        <el-table-column prop="wfDelay" label="延期" width="80" align="center" />
        <el-table-column prop="wfTotal" label="总计" width="80" align="center" />
      </el-table-column>

      <!-- SC统计 -->
      <el-table-column label="SC统计" align="center">
        <el-table-column prop="scOpen" label="开放" width="80" align="center" />
        <el-table-column prop="scDelay" label="延期" width="80" align="center" />
        <el-table-column prop="scTotal" label="总计" width="80" align="center" />
      </el-table-column>

      <!-- SP统计 -->
      <el-table-column label="SP统计" align="center">
        <el-table-column prop="spOpen" label="开放" width="80" align="center" />
        <el-table-column prop="spDelay" label="延期" width="80" align="center" />
        <el-table-column prop="spTotal" label="总计" width="80" align="center" />
      </el-table-column>

      <!-- SA统计 -->
      <el-table-column label="SA统计" align="center">
        <el-table-column prop="saOpen" label="开放" width="80" align="center" />
        <el-table-column prop="saDelay" label="延期" width="80" align="center" />
        <el-table-column prop="saTotal" label="总计" width="80" align="center" />
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="doSearch"
    />
  </div>
  <div v-if="showMode === 'chart'">
    tubiao
  </div>


</div>
</template>
<script >
import { exportSummaryReportExcel, getSummaryReportChart, getSummaryReportPage } from '@/api/scar/report'

export default {
  name: 'ReportOview',
  data() {
    return {
      loading: false,
      list: [],
      total: 0,
      queryParams: {
        supplierIds: [],
        year: '2025',
        pageNo: 1,
        pageSize: 10
      },
      showMode: 'list'
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      this.doSearch()
      this.getChartData()
    },
    doSearch() {
      this.loading = true
      getSummaryReportPage(this.queryParams).then(res => {
        if (res.code === 0) {
          this.list = res.data.list || []
          this.total = res.data.total || 0
        } else {
          this.$message.error(res.msg || '查询失败')
        }
      }).catch(error => {
        console.error('查询失败:', error)
        this.$message.error('查询失败，请稍后重试')
      }).finally(() => {
        this.loading = false
      })
    },
    resetQuery() {
      this.queryParams = {
        supplierIds: [],
        year: '2025',
        pageNo: 1,
        pageSize: 10
      }
      this.doSearch()
    },
    getChartData(){
      getSummaryReportChart(this.queryParams).then(res => {
        console.log(res)
      })
    },
    downloadReport() {
      exportSummaryReportExcel(this.queryParams).then(res => {
        this.$download.excel(res, '汇总报告.xlsx')
      })
    }
  }
}
</script>

<style scoped lang="scss">

</style>
