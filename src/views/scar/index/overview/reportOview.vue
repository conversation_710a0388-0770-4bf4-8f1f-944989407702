
<template>
<div>
  <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
    <el-input
      v-model="queryParams.supplierIds"
      style="flex: 0 1 40%"
      :placeholder="$t('供应商')"
      clearable
      @keyup.enter.native="doSearch"
    />
    <el-select
      style="width: 130px"
    v-model="queryParams.year"
      :placeholder="$t('年份')"
    >
      <el-option label="1" value="1" />
    </el-select>
    <el-button plain type="primary" @click="queryParams.pageNo = 1;doSearch();">{{ $t('common.search') }}</el-button>
    <el-button style="margin-left: 0" @click="resetQuery">{{ $t('common.reset') }}</el-button>

  </div>

</div>
</template>
<script >
import { getSummaryReportPage } from '@/api/scar/report'

export default {
  name: 'ReportOview',
  data() {
    return {
      queryParams: {
        searchText: '',
        year: '2025',
        pageNo: 1,
        pageSize: 10
      }
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      // 初始化数据
    },
    doSearch() {
      // 搜索方法
      getSummaryReportPage(this.queryParams).then(res => {
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    resetQuery() {
      // 重置方法
    }
  }
}
</script>

<style scoped lang="scss">

</style>
