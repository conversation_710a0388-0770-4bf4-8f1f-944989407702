<template>
  <div class="scar-list-container">
    <!-- 统计卡片 -->
    <StatisticsCard :item-arr="itemArr" />

    <!-- 搜索工作栏 -->
    <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
      <el-input
        v-model="queryParams.searchText"
        style="flex: 0 1 40%"
        :placeholder="$t('scar.scarCodeMaterialCodeSupplierName')"
        clearable
        @keyup.enter.native="doSearch"
      />
      <el-button plain type="primary" @click="queryParams.pageNo = 1;doSearch();">{{ $t('common.search') }}</el-button>
      <el-button style="margin-left: 0" @click="resetQuery">{{ $t('common.reset') }}</el-button>

      <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
        <el-button type="text">
          {{ $t('common.advancedSearch') }}
        </el-button>
        <i
          class="el-icon-arrow-up"
          :style="showSearch? '':{transform: 'rotate(180deg)'}"
          style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
        />
      </div>
    </div>

    <!-- 高级搜索表单 -->
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="170px">
      <el-form-item class="searchItem" :label="$t('auth.documentType')">
        <el-select v-model="queryParams.scarType" class="searchValue" clearable filterable>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.SCAR_RECORD_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('material.materialCode')">
        <el-input v-model="queryParams.materialCode" class="searchValue" :placeholder="$t('common.pleaseEnter')" clearable />
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('supplier.supplier')">
        <el-input v-model="queryParams.supplierText" class="searchValue" :placeholder="$t('order.pleaseEnterTheSuppliersShortNameOrCode')" clearable />
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('scar.scarStatus')">
        <el-select v-model="queryParams.scarStatus" multiple class="searchValue" clearable filterable>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.SCAR_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('material.materialDescription')">
        <el-input v-model="queryParams.materialDescription" class="searchValue" :placeholder="$t('common.pleaseEnter')" clearable />
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('material.factory')" prop="factoryId">
        <el-select v-model="queryParams.factoryId" class="searchValue" clearable multiple>
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.COMMON_FACTORY, 0)"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('supplier.purchasingOrganization')" class="searchItem">
        <el-select v-model="queryParams.orgId" class="searchValue" clearable multiple>
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.COMMON_PURCHASEORG, 0)"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('scar.qualityComplaintType')">
        <el-select v-model="queryParams.qualityComplaintType" class="searchValue" clearable filterable>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.SCAR_QUANTITY_COMPLAIN_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('material.category')" prop="category">
        <el-cascader
          v-model="queryParams.category"
          class="searchValue"
          filterable
          clearable
          :props="{ value: 'id',label:'name',multiple :true}"
          :options="categoryList"
          :placeholder="$t('material.category')"
        />
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('order.timeType')" prop="dateType">
        <el-select v-model="queryParams.dateType" class="searchValue" clearable>
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.SCAR_TIME_TYPE)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('scar.timelydelayed')" class="searchItem">
        <el-select v-model="queryParams.onTimeVal" class="searchValue" clearable @clear="clearOntimeVal">
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.SCAR_ON_TIME)"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('scar.currentHandler')" class="searchItem">
        <el-select v-model="queryParams.processorIds" class="searchValue" clearable multiple filterable>
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.COMMON_USERS, 0)"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="searchItem" label=" ">
        <el-date-picker
          v-model="queryParams.time"
          class="searchValue"
          type="daterange"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item :label="$t('scar.severity')" class="searchItem">
        <el-select v-model="queryParams.severityLevelList" class="searchValue" clearable multiple>
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.SCAR_SEVERITY_DEGREE)"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <!-- 列表 -->
    <vxe-grid
      ref="scarTable"
      :data="list"
      :loading="loading"
      v-bind="girdOption"
    >
      <template #scarStatus="{row}">
        <dict-tag :type="DICT_TYPE.SCAR_STATUS" :value="row.scarStatus" />
      </template>
      <template #supplierName="{row}">
          <copy-button type="text" @click="$router.push(`/supplier/cooperHis/${row.supplierId}?supplierId=${row.supplierId}&name=${row.supplierName}&viewOnly=false`)">{{ row.supplierName }}</copy-button>
      </template>
      <template #categoryId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="row.categoryId" />
      </template>
      <template #isRepetitionBad="{row}">
        <span v-if="row.scarType==='sp'" />
        <span v-else>{{ row.isRepetitionBad ? $t('auth.yes') : $t('auth.no') }}</span>
      </template>
      <template #qualityBadType="{row}">
        <dict-tag :type="DICT_TYPE.SCAR_DEFECT_TYPE" :value="row.qualityBadType" />
      </template>
      <template #scarType="{row}">
        <dict-tag :type="DICT_TYPE.SCAR_RECORD_TYPE" :value="row.scarType" />
      </template>
      <template #qualityComplaintType="{row}">
        <dict-tag :type="DICT_TYPE.SCAR_QUANTITY_COMPLAIN_TYPE" :value="row.qualityComplaintType" />
      </template>
      <template #factoryId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_FACTORY" :value="row.factoryId" />
      </template>
      <template #orgId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_PURCHASEORG" :value="row.orgId" />
      </template>
      <template #onTime="{row}">
        <span v-if="row.onTime" class="greenDot" />
        <span v-else class="redDot" />
      </template>
      <template #publisher="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.publishUserId" />
      </template>
      <template #scarCode="{row}">
        <copyButton
          type="text"
          @click="$router.push(`/scar/${row.scarType}/see/${row.scarCode}?id=${row.id}&viewOnly=1`)"
        >
          {{ row.scarCode }}
        </copyButton>
      </template>
      <template #poNumber="{row}">
        <copy-button type="text" @click="$router.push(`/om/poInfo/${row.poId}?no=${row.poNumber}&viewOnly=true`)">{{ row.poNumber }}</copy-button>
      </template>

      <template #toolbar_buttons>
        <el-row :gutter="10" style="width: 100%" class="mb8">
          <el-col :span="1.5">
            <el-dropdown
              v-has-permi="['scar:record:create']"
            >
              <el-button type="primary" size="mini">
                {{ $t('scar.newScar') }}
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-for="scarType in getDictDatas(DICT_TYPE.SCAR_RECORD_TYPE, 0)"
                  :key="scarType.id"
                >
                  <el-button type="text" @click="$router.push(`/scar/${scarType.value}/0?id=`)">
                    {{ scarType.label }}
                  </el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="primary"
              size="mini"
              plain
              icon="el-icon-download"
              :loading="loadingButton"
              @click="doExportPageRecord()"
            >  {{ $t('order.download') }}</el-button>
          </el-col>
          <right-toolbar :list-id="girdOption.id" :show-search.sync="showSearch" :custom-columns.sync="girdOption.columns" @queryTable="doSearch" />
        </el-row>
      </template>
      <template #operate="{row}">
        <OperateDropDown
          :menu-item="[
            {
              name: $t('scar.documentExtension'),
              show: ['supplier_reply', 'sqe_review', 'countersign'].includes(row.scarStatus) && $store.getters.isExternal === 1,
              action: (row) => beforeOpenPostponeRecord(row),
              para:row.id
            },
            {
              name: $t('scar.notPublish'),
              show: ['new', 'to_be_released'].includes(row.scarStatus) && $store.getters.isExternal === 1,
              action: (row) => doCancel(row),
              para: [row.id]
            },
            {
              name: $t('common.del'),
              show: ['new', 'to_be_released'].includes(row.scarStatus) && $store.getters.isExternal === 1 && (row.createType === 'MANUALLY' || row.createType === 'manually'),
              action: (row) => doDel(row),
              para: [row.id]
            },
            {
              name: row.scarStatus === 'new' ? $t('common.edit'): getDictDataLabel(DICT_TYPE.SCAR_STATUS,row.scarStatus),
              show: canEditRecord(row),
              action: (row) =>
                $router.push(`/scar/${row.scarType}/${row.scarCode}?id=${row.id}`),
              para:row
            },
            {
              name: $t('common.copyAddress'),
              show: canEditRecord(row) && $store.getters.isExternal === 1 && row
                .scarStatus==='supplier_reply',
              action: (row) => copyAddress(row),
              para:row.id
            },
            {
              name: $t('system.transfer'),
              show: canEditRecord(row) && $store.getters.isExternal === 1,
              action: (row) => handleUpdateAssignee(row),
              para:row.id
            },
            {
              name: $t('auth.downloadReport'),
              show: ['completed'].includes(row.scarStatus) && $store.getters.permissions.includes('scar:file-rel:query'),
              action: (row) => downloadScarReport(row),
              para:row
            },
            {
              name: $t('common.operationRecord'),
              show:true,
              action: (row) => handleLog(row),
              para:row
            }
          ]"
        />
      </template>
    </vxe-grid>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="doSearch"
    />

    <!--    质量单据的操作记录-->
    <el-dialog :visible="openOperateRecord" :title="$t('common.operationRecord')"
class="logDialog" @close="openOperateRecord = false">
      <el-table :data="operateRecordList" style="width: 100%">
        <el-table-column :label="$t('auth.operationNode')" width="180">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.SCAR_OPERATE_STEP" :value="scope.row.operatorSection" />
          </template>
        </el-table-column>
        <el-table-column prop="operator" :label="$t('system.operator')" width="180">
          <template #default="scope">
            <span>{{ scope.row.operator }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="recordCreateTime" :label="$t('common.createTime')" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.recordCreateTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="operateTime" :label="$t('system.operationTime')" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.operateTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('common.operate')" width="180">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.SCAR_OPERATE_TYPE" :value="scope.row.operate" />
          </template>
        </el-table-column>
        <el-table-column prop="checkContent" :label="$t('auth.approvalComments')" width="180" />
      </el-table>
    </el-dialog>

    <!--    单据延期的弹框-->
    <el-dialog :visible="openPostponeRecord" :title="$t('scar.documentExtension')" @close="beforeClosePostponeRecord">
      <el-form ref="postponeRecordForm" :model="postponeParams">
        <el-form-item
          :label="$t('scar.latestRequestForSupplierResponseDate')"
          :rules="{ required: true, message: $t('scar.extensionDateRequired'), trigger: ['blur', 'change'] }"
          prop="postponeDate"
        >
          <el-date-picker
            v-model="postponeParams.postponeDate"
            label-width="100px"
            type="date"
            :placeholder="$t('common.pleaseSelectADate')"
            :picker-options="pickerOptions"
          />
        </el-form-item>
        <el-form-item
          :label="$t('scar.reasonForDelay')"
          :rules="{ required: true, message: $t('scar.reasonForExtensionIsRequired'), trigger: ['blur', 'change'] }"
          prop="postponeReason"
        >
          <el-input
            v-model="postponeParams.postponeReason"
            type="textarea"
            :rows="3"
            :placeholder="$t('rfq.pleaseEnterTheContent')"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button size="mini" @click="beforeClosePostponeRecord">{{ $t('common.cancel') }}</el-button>
        <el-button size="mini" type="primary" @click="doPostponeRecord">{{ $t('common.confirm') }}</el-button>

      </div>
    </el-dialog>

    <!--    新建质量单据弹框-->
    <el-dialog :visible="openCreateRecord" :title="$t('scar.selectDocument')" @close="afterCloseDialog">
      <el-radio-group v-model="newScarType">
        <div v-for="dict in getDictDatas(DICT_TYPE.SCAR_RECORD_TYPE, 0)" :key="dict.id">
          <el-radio
            style="padding-top: 15px"
            :label="dict.value"
          >
            {{ dict.label }}</el-radio>
        </div>
      </el-radio-group>
      <div slot="footer">
        <el-button size="mini" @click="afterCloseDialog">{{ $t('common.cancel') }}</el-button>
        <el-button
          size="mini"
          type="primary"
          @click="toCreateNewRecord"
        >{{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>

    <!-- 对话框(转派审批人) -->
    <el-dialog :title="$t('system.transferApprover')" :visible.sync="updateAssignee.open" append-to-body width="500px">
      <el-form ref="updateAssigneeForm" :model="updateAssignee.form" :rules="updateAssignee.rules" label-width="110px">
        <el-form-item :label="$t('system.newApprover')" prop="assigneeUserId">
          <el-select v-model="updateAssignee.form.assigneeUserId" style="width: 100%" class="searchValue" clearable filterable>
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.COMMON_USERS, 0)"
              :key="parseInt(item.id)"
              :label="item.nickname"
              :value="parseInt(item.id)"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelUpdateAssigneeForm">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitUpdateAssigneeForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>

    <el-dialog
      :title="$t('common.shareInvitationLink')"
      :visible.sync="ShareInvitationLink.open"
      append-to-body
      width="500px"
    >
      <el-form :model="ShareInvitationLink" label-width="130px">
        <span>{{ ShareInvitationLink.supplierInvitationAddress }}</span>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-clipboard:copy="ShareInvitationLink.supplierInvitationAddress" v-clipboard:success="()=>{$modal.msgSuccess($t('system.successfullyCopied'));ShareInvitationLink.open =false}" type="primary">{{ $t('order.copy') }}</el-button>

        <el-button @click="ShareInvitationLink.open = false">{{ $t('common.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { parseTime, handleTree } from '@/utils/ruoyi'
import { DICT_TYPE, getDictDataLabel, getDictDatas } from '@/utils/dict'
import {
  getDataOperateRecordPage,
  pageRecord,
  exportPageRecord,
  postponeRecord,
  undoRecord,
  deleteRecord,
  canPostpone,
  downloadScarRecordAllFilesToZip,
  updateTaskAssignee,
  getScarSupplierShareInvitationLink,
  exportPageTestRecord
} from '@/api/scar'
import dayjs from 'dayjs'
import OperateDropDown from '@/components/OperateDropDown/index.vue'

export default {
  name: 'ScarList',
  components: {
    OperateDropDown
  },
  props: {
    itemArr: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      DICT_TYPE,
      getDictDataLabel,
      parseTime,
      // 涉及到数据库更新操作的按钮操作增加loading操作，防止重复操作
      loadingButton: false,
      // 单据操作记录的弹框控制
      openOperateRecord: false,
      operateRecordList: [],
      // 创建单据的弹框控制
      openCreateRecord: false,
      // 单据类型
      newScarType: '',
      // 单据延期的弹框控制
      openPostponeRecord: false,
      postponeParams: {
        id: '',
        // 延期日期
        postponeDate: '',
        // 延期理由
        postponeReason: ''
      },
      // 转办人
      updateAssignee: {
        open: false,
        form: {
          assigneeUserId: undefined
        },
        rules: {
          assigneeUserId: [{ required: true, message: this.$t('system.newApproverCannotBeEmpty'), trigger: 'change' }]
        }
      },
      // 分享邀请链接
      ShareInvitationLink: {
        // 是否显示弹出层
        open: false,
        // 供应商名称
        supplierInvitationAddress: ''
      },
      pickerOptions: {
        disabledDate(time) {
          return dayjs().isAfter(dayjs(time), 'day')
        }
      },
      categoryList: [],
      loading: false,
      showSearch: false,
      total: 0,
      list: [],
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        scarType: '',
        materialCode: '',
        supplierText: '',
        scarStatus: [],
        materialDescription: '',
        orgId: [],
        factoryId: [],
        category: [],
        time: [],
        dateType: '',
        onTime: '',
        onTimeVal: '',
        processorIds: [],
        searchText: '',
        severityLevelList: [],
        sortBy: '',
        sortField: ''
      },
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'scar',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        filterConfig: {
          remote: true
        },
        columns: [
          { type: 'checkbox', width: 30, fixed: 'left' },
          { field: 'operate', slots: { default: 'operate' }, fixed: 'left', title: this.$t('common.operate'), visible: true, showOverflow: false, width: 35 },
          { title: this.$t('scar.scarCode'), field: 'scarCode', slots: { default: 'scarCode' }, visible: true, width: 110, fixed: 'left' },
          { title: this.$t('PO#'), field: 'poNumber', slots: { default: 'poNumber' }, visible: true, width: 110 },
          { title: this.$t('scar.currentHandler'), field: 'currentUsers', visible: true, width: 135 },
          { title: this.$t('supplier.supplierName'), slots: { default: 'supplierName' }, field: 'supplierName', visible: true, width: 135 },
          { title: this.$t('common.status'), slots: { default: 'scarStatus' }, field: 'scarStatus', visible: true, width: 135 },
          { title: this.$t('material.materialCode'), field: 'materialCode', visible: true, width: 135 },
          { title: this.$t('material.materialDescription'), field: 'materialDescription', visible: true, width: 135 },
          { title: this.$t('material.category'), slots: { default: 'categoryId' }, field: 'categoryId', visible: true, width: 135 },
          { title: this.$t('scar.areThereAnyDuplicateDefects'), slots: { default: 'isRepetitionBad' }, field: 'isRepetitionBad', visible: true, width: 135 },
          { title: this.$t('scar.typesOfDefects'), slots: { default: 'qualityBadType' }, field: 'qualityBadType', visible: true, width: 135 },
          { title: this.$t('auth.documentType'), slots: { default: 'scarType' }, field: 'scarType', visible: true, width: 135 },
          { title: this.$t('scar.qualityComplaintType'), slots: { default: 'qualityComplaintType' }, field: 'qualityComplaintType', visible: true, width: 135 },
          { title: this.$t('material.factory'), slots: { default: 'factoryId' }, field: 'factoryId', visible: true, width: 135 },
          { title: this.$t('supplier.purchasingOrganization'), slots: { default: 'orgId' }, field: 'orgId', visible: true, width: 135 },
          { title: this.$t('scar.releaseDate'), sortable: true, field: 'publishDate', visible: true, width: 135 },
          { title: this.$t('scar.problemDescription'), field: 'questionDescText', visible: true, width: 135 },
          { title: this.$t('scar.rootCauseAnalysis'), field: 'rootCauseAnalysisText', visible: true, width: 135 },
          { title: this.$t('scar.permanentCorrectiveMeasures'), field: 'permanentCorrectiveActionText', visible: true, width: 135 },
          { title: this.$t('scar.requestSupplierResponseDate'), sortable: true, field: 'permanentActionDate', visible: true, width: 135 },
          { title: this.$t('scar.suppliersFirstResponseDate'), sortable: true, field: 'supplierFirstReplyDate', visible: true, width: 135 },
          { title: this.$t('scar.reviewDate'), sortable: true, field: 'approveDate', visible: true, width: 135 },
          { title: this.$t('common.publisher'), field: 'publishUserId', slots: { default: 'publisher' }, visible: true, width: 135 },
          { title: this.$t('scar.approvalDate'), sortable: true, field: 'passDate', visible: true, width: 135 },
          { title: this.$t('scar.timelydelayed'), slots: { default: 'onTime' }, field: 'onTime', visible: true, width: 135, align: 'center' }
        ],
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      }
    }
  },
  mounted() {
    this.doSearch()
    this.getCategories()
  },
  methods: {
    getDictDatas,
    handleTree,
    // clear及时/延迟的事件
    clearOntimeVal() {
      this.queryParams.onTime = ''
    },
    // 搜索方法
    doSearch() {
      const errMsg = this.validateDateType()
      if (errMsg) {
        this.$message.error(errMsg)
        return
      }
      this.loading = true
      if (this.queryParams.time?.length) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      if (this.queryParams.category) {
        this.queryParams.categoryIds = this.queryParams.category.map(item => item?.at(-1))
      }
      // 总览列表高级搜索条件-及时/延迟
      if (this.queryParams.onTimeVal) {
        if (this.queryParams.onTimeVal === 'on_time') {
          this.queryParams.onTime = true
        } else {
          this.queryParams.onTime = false
        }
      }
      pageRecord(this.queryParams).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      }).catch(_ => {

      })
    },
    validateDateType() {
      if (this.queryParams.time?.length > 0 || this.queryParams.dateType) {
        if (this.queryParams.dateType && (!this.queryParams.time || this.queryParams.time?.length === 0)) {
          return this.$t('scar.pleaseSelectATimeInterval')
        }
        if (this.queryParams.time?.length > 0 && !this.queryParams.dateType) {
          return this.$t('scar.pleaseSelectATimeType')
        }
      }
    },
    // 导出质量单据数据
    async doExportPageRecord(test) {
      const errMsg = this.validateDateType()
      if (errMsg) {
        this.$message.error(errMsg)
        return
      }
      if (this.queryParams.time?.length) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      if (this.queryParams.category) {
        this.queryParams.categoryIds = this.queryParams.category.map(item => item?.at(-1))
      }
      // 总览列表高级搜索条件-及时/延迟
      if (this.queryParams.onTimeVal) {
        if (this.queryParams.onTimeVal === 'on_time') {
          this.queryParams.onTime = true
        } else {
          this.queryParams.onTime = false
        }
      }
      this.loadingButton = true
      let res = null
      try {
        if (test) {
          res = await exportPageTestRecord(this.queryParams)
        } else {
          res = await exportPageRecord(this.queryParams)
        }
        this.$download.excel(res, this.$t('scar.qualityDocumentList'))
        this.loadingButton = false
      } catch (e) {
        this.loadingButton = false
      }
    },
    /**
     * 复制链接
     * @param scarId
     */
    copyAddress(scarId) {
      getScarSupplierShareInvitationLink(scarId).then(res => {
        this.ShareInvitationLink.open = true
        this.ShareInvitationLink.supplierInvitationAddress = res.data
      })
    },
    // 质量单据列表重置并搜索
    resetQuery() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        scarType: '',
        materialCode: '',
        supplierText: '',
        scarStatus: [],
        materialDescription: '',
        orgId: [],
        category: [],
        factoryId: [],
        dateType: '',
        onTime: '',
        onTimeVal: '',
        processorIds: [],
        searchText: '',
        sortBy: '',
        sortField: '',
        time: []
      }
      this.doSearch()
    },
    // 打开单据延期弹框之前的事件处理
    beforeOpenPostponeRecord(id) {
      // 校验单据的状态
      canPostpone(id).then(res => {
        if (!res.data) {
          this.$message.warning(this.$t('scar.theCurrentDocumentDoesNotSupportExtension'))
        } else {
          this.postponeParams.id = id
          this.openPostponeRecord = true
        }
      })
    },
    // 单据 撤销
    doCancel(ids) {
      this.$modal.confirm(this.$t('scar.areYouSureYouWantToCancelTheCurrentDocument')).then(() => {
        undoRecord({
          scarIds: ids.join(',')
        }).then(res => {
          this.doSearch()
        })
      })
    },
    // 单据 删除
    doDel(ids) {
      this.$modal.confirm(this.$t('scar.areYouSureYouWantToDeleteTheCurrentDocument')).then(() => {
        deleteRecord({
          scarIds: ids.join(',')
        }).then(res => {
          this.doSearch()
        })
      })
    },
    // 日志操作
    handleLog(row) {
      this.openOperateRecord = true
      getDataOperateRecordPage(row.id).then(res => {
        this.operateRecordList = res.data
      })
    },
    // 获取品类
    getCategories() {
      this.categoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0), 'id'))
    },
    // 当前用户是否可以进行编辑
    canEditRecord(row) {
      // 1.当前质量单据状态为 会签 且当前用户的操作节点状态为 已提交 则不允许进入编辑
      if (row.scarStatus === 'countersign' && row.sectionStatus === 'submit') {
        return false
      }
      // 2.由于数据权限的存在，当前用户可能通过采购组织（or 工厂（目前暂无该种数据权限））拉出非自己的质量单据，因此需要根据当前办理人判断是否因此 编辑按钮
      if (!row.currentUserIds?.includes(this.$store.getters.userId)) {
        return false
      }
      // 3.当前质量单据为已完成、已撤销 则不允许进入编辑
      return !['completed', 'revoked'].includes(row.scarStatus)
    },
    // 质量单据#已完成状态时下载报告pdf
    downloadScarReport(row) {
      if (row.scarStatus !== 'completed') {
        this.$message.error(this.$t('auth.theCurrentDocumentDoesNotSupportDownloadingReports'))
        return
      }
      downloadScarRecordAllFilesToZip({ scarId: row.id }).then(res => {
        this.$download.zip(res, row.scarCode + '.zip')
      }).catch(_ => {

      })
    },
    /** 处理转派审批人 */
    handleUpdateAssignee(id) {
      // 设置表单
      this.resetUpdateAssigneeForm()
      this.updateAssignee.form.id = id
      // 设置为打开
      this.updateAssignee.open = true
    },

    // 重置转派审批人
    resetUpdateAssigneeForm() {
      this.updateAssignee.form = {
        id: undefined,
        assigneeUserId: undefined
      }
      this.resetForm('updateAssigneeForm')
    },

    // 提交转派审批人
    submitUpdateAssigneeForm() {
      this.$refs['updateAssigneeForm'].validate(valid => {
        if (!valid) {
          return
        }
        updateTaskAssignee(this.updateAssignee.form).then(response => {
          this.$modal.msgSuccess(this.$t('system.successfullyTransferredTask'))
          this.updateAssignee.open = false
          this.doSearch()
        })
      })
    },

    // 取消转派审批人
    cancelUpdateAssigneeForm() {
      this.updateAssignee.open = false
      this.resetUpdateAssigneeForm()
    },

    // 单据延期
    doPostponeRecord() {
      this.$refs.postponeRecordForm.validate(async valid => {
        if (!valid) {
          return
        }
        postponeRecord({
          scarId: this.postponeParams.id,
          latestDate: this.postponeParams.postponeDate,
          reason: this.postponeParams.postponeReason
        }).then(res => {
          this.$message.success(this.$t('order.operationSucceeded'))
          this.openPostponeRecord = false
          this.doSearch()
        })
      })
    },

    // 关闭单据延期弹框的事件处理
    beforeClosePostponeRecord() {
      this.openPostponeRecord = false
      this.postponeParams.postponeDate = ''
      this.postponeParams.postponeReason = ''
      if (this.$refs['postponeRecordForm']) {
        this.$refs['postponeRecordForm'].resetFields()
      }
    },

    // 创建新的单据触发
    toCreateNewRecord() {
      if (!this.newScarType) {
        this.$message.error(this.$t('scar.pleaseSelectADocumentType'))
        return
      }
      this.$router.push(`/scar/${this.newScarType}/0?id=`)
    },

    // 关闭新建单据类型的弹框处理
    afterCloseDialog() {
      this.openCreateRecord = false
      this.newScarType = ''
    }
  }
}
</script>

<style lang="scss" scoped>
.scar-list-container {
  padding: 0;
}

.greenDot {
  height: 20px;
  width: 20px;
  background-color: #008000;
  border-radius: 50%;
  display: inline-block;
}
.redDot {
  height: 20px;
  width: 20px;
  background-color: #ff0000;
  border-radius: 50%;
  display: inline-block;
}
.searchItem{
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;
  ::v-deep .el-form-item__content{
    width: calc(100% - 170px);
  }
}
.searchValue{
  width: 95%;
}
</style>
